import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { MkdLoader } from "@/components/MkdLoader";
import { DeliveryPartnerModal } from "@/components/DeliveryPartnerModal";
import CircleCheckMarkIcon from "../../../assets/svgs/CircleCheckMarkIcon";
import CloudUploadIcon from "../../../assets/svgs/CloudUploadIcon";
import FileAudioIcon from "../../../assets/svgs/FileAudioIcon";
import SpiralArrow from "../../../assets/svgs/SpiralArrow";
import PaperPlaneIcon from "../../../assets/svgs/PaperPlaneIcon";
import MicrophoneIcon from "../../../assets/svgs/MicrophoneIcon";

interface IUserProfile {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
  member_since?: string | null;
  date_of_birth?: string | null;
  gender?: string | null;
  bio?: string | null;
}

interface IPasswordForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const MemberProfilePage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [activeTab, setActiveTab] = useState("personal");
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [deliveryAgentStatus, setDeliveryAgentStatus] =
    useState("not_registered"); // "not_registered", "pending", "approved", "rejected"
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<{
    [key: string]: boolean;
  }>({});
  const [isRecording, setIsRecording] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const profileSchema = yup.object({
    email: yup.string().email("Invalid email").required("Email is required"),
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
    phone: yup.string().nullable(),
    photo: yup.string().nullable(),
    address: yup.string().nullable(),
    city: yup.string().nullable(),
    state: yup.string().nullable(),
    zip_code: yup.string().nullable(),
    country: yup.string().nullable(),
    date_of_birth: yup.string().nullable(),
    gender: yup.string().nullable(),
    bio: yup.string().nullable(),
  });

  const passwordSchema = yup.object({
    current_password: yup.string().required("Current password is required"),
    new_password: yup
      .string()
      .min(6, "Password must be at least 6 characters")
      .required("New password is required"),
    confirm_password: yup
      .string()
      .oneOf([yup.ref("new_password")], "Passwords must match")
      .required("Please confirm your password"),
  });

  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    setValue,
    formState: { errors: profileErrors },
  } = useForm<IUserProfile>({
    resolver: yupResolver(profileSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    reset: resetPasswordForm,
    formState: { errors: passwordErrors },
  } = useForm<IPasswordForm>({
    resolver: yupResolver(passwordSchema),
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    try {
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile",
        method: "GET",
      });

      console.log("Profile API Response:", response);

      if (!response.error) {
        const data = response.data;
        const profileData = data?.profile || {};

        // Process and validate profile data
        const processedProfile = {
          email: String(data?.email || ""),
          first_name: String(profileData.first_name || ""),
          last_name: String(profileData.last_name || ""),
          phone: profileData.phone || null,
          photo: profileData.profile_photo || profileData.photo || null,
          address: profileData.location || profileData.address || null,
          city: profileData.city || null,
          state: profileData.state || null,
          zip_code: profileData.zip_code || null,
          country: profileData.country || null,
          member_since: data?.memberSince || null,
          date_of_birth: profileData.date_of_birth || null,
          gender: profileData.gender || null,
          bio: profileData.bio || null,
        };

        // Set profile state
        setProfile(processedProfile);

        // Set form values
        setValue("email", processedProfile.email);
        setValue("first_name", processedProfile.first_name);
        setValue("last_name", processedProfile.last_name);
        setValue("phone", processedProfile.phone || "");
        setValue("photo", processedProfile.photo || "");
        setValue("address", processedProfile.address || "");
        setValue("city", processedProfile.city || "");
        setValue("state", processedProfile.state || "");
        setValue("zip_code", processedProfile.zip_code || "");
        setValue("country", processedProfile.country || "");
        setValue("date_of_birth", processedProfile.date_of_birth || "");
        setValue("gender", processedProfile.gender || "");
        setValue("bio", processedProfile.bio || "");

        // Process verification data from documents array
        if (data?.documents && Array.isArray(data.documents)) {
          const verificationData: any = {};

          data.documents.forEach((doc: any) => {
            switch (doc.document_type) {
              case "government_id":
                verificationData.government_id_status = doc.status;
                verificationData.government_id_message = doc.rejection_reason;
                break;
              case "selfie_verification":
                verificationData.selfie_status = doc.status;
                verificationData.selfie_message = doc.rejection_reason;
                break;
              case "voice_verification":
                verificationData.voice_status = doc.status;
                verificationData.voice_message = doc.rejection_reason;
                break;
            }
          });

          setVerificationStatus(verificationData);
        } else if (data?.verification) {
          // Fallback to verification object if documents array is not available
          setVerificationStatus(data.verification);
        }

        // Set delivery application status
        if (data?.deliveryApplication) {
          setDeliveryAgentStatus(
            String(data.deliveryApplication.status || "not_registered")
          );
        } else {
          setDeliveryAgentStatus("not_registered");
        }
      } else {
        setError(String(response.message || "Failed to load profile"));
      }
    } catch (error: any) {
      console.error("Error fetching profile:", error);
      setError(String(error?.message || "Failed to load profile"));
    } finally {
      setLoading(false);
    }
  }, []);

  const onProfileSubmit = useCallback(async (data: IUserProfile) => {
    setSaving(true);
    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile",
        method: "PUT",
        body: {
          first_name: data.first_name,
          last_name: data.last_name,
          phone: data.phone,
          address: data.address,
          city: data.city,
          state: data.state,
          zip_code: data.zip_code,
          country: data.country,
          profile_photo: data.photo,
          date_of_birth: data.date_of_birth,
          gender: data.gender,
          bio: data.bio,
        },
      });

      console.log("Update Profile API Response:", response);

      if (!response.error) {
        success("Profile updated successfully!");
        setProfile(data);
      } else {
        showError(String(response.message || "Failed to update profile"));
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);
      showError(String(error?.message || "Failed to update profile"));
    } finally {
      setSaving(false);
    }
  }, []);

  const onPasswordSubmit = useCallback(async (data: IPasswordForm) => {
    setSaving(true);
    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile/change-password",
        method: "POST",
        body: {
          currentPassword: data.current_password,
          newPassword: data.new_password,
        },
      });

      console.log("Change Password API Response:", response);

      if (!response.error) {
        success("Password changed successfully!");
        resetPasswordForm();
      } else {
        showError(String(response.message || "Failed to change password"));
      }
    } catch (error: any) {
      console.error("Error changing password:", error);
      showError(String(error?.message || "Failed to change password"));
    } finally {
      setSaving(false);
    }
  }, []);

  const handleDeliveryApplicationSubmit = () => {
    // When application is submitted, change status to pending
    setDeliveryAgentStatus("pending");
    setShowDeliveryModal(false);
  };

  const handlePhotoUpload = useCallback(
    async (file: File) => {
      try {
        setUploadingPhoto(true);

        // First upload the file to get the URL
        const uploadResponse = await sdk.upload(file);

        if (uploadResponse.error) {
          showError(String(uploadResponse.message || "Failed to upload file"));
          return;
        }

        const photoUrl = uploadResponse.data?.url || uploadResponse.url;

        // Then update the profile with the photo URL
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/profile/photo",
          method: "POST",
          body: { photoUrl },
        });

        console.log("Photo Upload API Response:", response);

        if (!response.error) {
          success("Profile photo updated successfully!");
          fetchProfile(); // Refresh profile data
        } else {
          showError(String(response.message || "Failed to upload photo"));
        }
      } catch (error: any) {
        console.error("Error uploading photo:", error);
        showError(String(error?.message || "Failed to upload photo"));
      } finally {
        setUploadingPhoto(false);
      }
    },
    [sdk, success, showError, fetchProfile]
  );

  const handlePhotoRemove = useCallback(async () => {
    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/profile/photo",
        method: "DELETE",
      });

      console.log("Photo Remove API Response:", response);

      if (!response.error) {
        success("Profile photo removed successfully!");
        fetchProfile(); // Refresh profile data
      } else {
        showError(String(response.message || "Failed to remove photo"));
      }
    } catch (error: any) {
      console.error("Error removing photo:", error);
      showError(String(error?.message || "Failed to remove photo"));
    }
  }, []);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleDocumentUpload = useCallback(
    async (file: File, documentType: string) => {
      try {
        setUploadingDocument((prev) => ({ ...prev, [documentType]: true }));

        const formData = new FormData();
        formData.append("file", file);
        formData.append("document_type", documentType);

        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/verification/upload-document",
          method: "POST",
          body: formData,
        });

        console.log(`Upload ${documentType} API Response:`, response);

        if (!response.error) {
          success(`${documentType.replace("_", " ")} uploaded successfully!`);
          fetchProfile(); // Refresh profile data to get updated verification status
        } else {
          showError(
            String(response.message || `Failed to upload ${documentType}`)
          );
        }
      } catch (error: any) {
        console.error(`Error uploading ${documentType}:`, error);
        showError(String(error?.message || `Failed to upload ${documentType}`));
      } finally {
        setUploadingDocument((prev) => ({ ...prev, [documentType]: false }));
      }
    },
    [sdk, success, showError, fetchProfile]
  );

  const handleVoiceRecording = useCallback(async () => {
    if (isRecording) {
      // Stop recording logic would go here
      setIsRecording(false);
      success("Recording stopped. Please upload the audio file.");
    } else {
      // Start recording logic would go here
      setIsRecording(true);
      success("Recording started. Click again to stop.");
    }
  }, [isRecording, success]);

  const handleSubmitForReview = useCallback(async () => {
    try {
      setSaving(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/verification/submit",
        method: "POST",
      });

      console.log("Submit Verification API Response:", response);

      if (!response.error) {
        success(
          "Verification submitted successfully! We'll review your documents within 24 hours."
        );
        fetchProfile(); // Refresh profile data
      } else {
        showError(String(response.message || "Failed to submit verification"));
      }
    } catch (error: any) {
      console.error("Error submitting verification:", error);
      showError(String(error?.message || "Failed to submit verification"));
    } finally {
      setSaving(false);
    }
  }, [sdk, success, showError, fetchProfile]);

  const handleDeleteAccount = useCallback(async () => {
    try {
      setSaving(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/account/delete",
        method: "DELETE",
      });

      console.log("Delete Account API Response:", response);

      if (!response.error) {
        success(
          "Account deleted successfully. You will be redirected to the homepage."
        );
        // Redirect to homepage or login page after a delay
        setTimeout(() => {
          window.location.href = "/";
        }, 2000);
      } else {
        showError(String(response.message || "Failed to delete account"));
      }
    } catch (error: any) {
      console.error("Error deleting account:", error);
      showError(String(error?.message || "Failed to delete account"));
    } finally {
      setSaving(false);
      setShowDeleteConfirm(false);
    }
  }, [sdk, success, showError]);

  const handleExportData = useCallback(async () => {
    try {
      setSaving(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/account/export",
        method: "GET",
      });

      console.log("Export Data API Response:", response);

      if (!response.error) {
        // Create and download the data file
        const dataStr = JSON.stringify(response.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: "application/json" });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `ebadollar-profile-data-${new Date().toISOString().split("T")[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        success("Profile data exported successfully!");
      } else {
        showError(String(response.message || "Failed to export data"));
      }
    } catch (error: any) {
      console.error("Error exporting data:", error);
      showError(String(error?.message || "Failed to export data"));
    } finally {
      setSaving(false);
    }
  }, [sdk, success, showError]);

  const renderVerificationStatus = (status: string, type: string) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case "approved":
          return "bg-green-50 border-green-200 text-green-700";
        case "rejected":
          return "bg-red-50 border-red-200 text-red-600";
        default:
          return "bg-yellow-50 border-yellow-200 text-yellow-700";
      }
    };

    const getStatusText = (status: string) => {
      switch (status) {
        case "approved":
          return "[Verified]";
        case "rejected":
          return "[Rejected]";
        default:
          return "[Pending]";
      }
    };

    return (
      <div className={`mt-4 p-3 border rounded-md ${getStatusColor(status)}`}>
        <div className="flex items-center">
          {status === "approved" && (
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
          )}
          <span className="font-medium text-sm mr-2">
            {getStatusText(status)}
          </span>
          <span className="text-sm">
            {status === "approved" && `${type} verified successfully`}
            {status === "rejected" && `${type} verification failed`}
            {status === "pending" && `${type} verification pending`}
          </span>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex h-full items-center justify-center">
          <MkdLoader />
        </div>
      </MemberWrapper>
    );
  }

  if (error && !profile) {
    return (
      <MemberWrapper>
        <div className="h-full bg-[#0F2C59] overflow-auto">
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-white mb-4">{error}</p>
              <button
                onClick={fetchProfile}
                className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-white mb-2">
              Profile Settings
            </h1>
            {/* Temporary test button */}
            <button
              onClick={() =>
                setDeliveryAgentStatus(
                  deliveryAgentStatus === "not_registered"
                    ? "pending"
                    : "not_registered"
                )
              }
              className="text-sm bg-yellow-500 text-black px-3 py-1 rounded mt-2"
            >
              Toggle Status (Test)
            </button>
          </div>

          {/* Personal Information Section */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Personal Information
            </h2>

            <form onSubmit={handleProfileSubmit(onProfileSubmit)}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Photo Section */}
                <div className="md:col-span-2 flex items-start gap-4 mb-6">
                  <div className="flex flex-col items-center">
                    <div className="w-24 h-24 rounded-full bg-gray-300 flex items-center justify-center mb-2 relative">
                      {profile?.photo ? (
                        <img
                          src={profile.photo}
                          alt="Profile"
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-3xl text-gray-600">👤</span>
                      )}
                      {uploadingPhoto && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <label className="text-sm text-[#0F2C59] hover:underline cursor-pointer">
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handlePhotoUpload(file);
                            }
                          }}
                          disabled={uploadingPhoto}
                        />
                        {uploadingPhoto ? "Uploading..." : "Upload"}
                      </label>
                      {profile?.photo && (
                        <button
                          type="button"
                          onClick={handlePhotoRemove}
                          className="text-sm text-gray-500 hover:underline"
                          disabled={uploadingPhoto}
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Profile Photo
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      Upload a clear photo of yourself. This helps other users
                      recognize you.
                    </p>
                    <p className="text-xs text-gray-500">
                      Recommended: Square image, at least 200x200 pixels. Max
                      file size: 5MB.
                    </p>
                  </div>
                </div>

                {/* First Name */}
                <div>
                  <MkdInputV2
                    {...registerProfile("first_name")}
                    errors={profileErrors.first_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>First Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Alex"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Last Name */}
                <div>
                  <MkdInputV2
                    {...registerProfile("last_name")}
                    errors={profileErrors.last_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Last Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Johnson"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Email Address */}
                <div>
                  <MkdInputV2
                    {...registerProfile("email")}
                    errors={profileErrors.email?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Email Address*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="<EMAIL>"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                  <p className="text-xs text-gray-500 mt-1">
                    We'll always let you know about important changes, but you
                    pick what else you want to hear about.
                  </p>
                </div>

                {/* Phone Number */}
                <div>
                  <MkdInputV2
                    {...registerProfile("phone")}
                    errors={profileErrors.phone?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="+****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-xs text-gray-500">Verified</span>
                  </div>
                </div>

                {/* Address */}
                <div className="md:col-span-2">
                  <MkdInputV2
                    {...registerProfile("address")}
                    errors={profileErrors.address?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Street Address</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="123 Main Street, Apt 4B"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* City */}
                <div>
                  <MkdInputV2
                    {...registerProfile("city")}
                    errors={profileErrors.city?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>City</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="New York"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* State */}
                <div>
                  <MkdInputV2
                    {...registerProfile("state")}
                    errors={profileErrors.state?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>State/Province</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="NY"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* ZIP Code */}
                <div>
                  <MkdInputV2
                    {...registerProfile("zip_code")}
                    errors={profileErrors.zip_code?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>ZIP/Postal Code</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="10001"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Country */}
                <div>
                  <MkdInputV2
                    {...registerProfile("country")}
                    errors={profileErrors.country?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Country</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="United States"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Member Since */}
                <div>
                  <MkdInputV2
                    errors=""
                    value={
                      profile?.member_since
                        ? new Date(profile.member_since).toLocaleDateString()
                        : ""
                    }
                    disabled
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Member Since</MkdInputV2.Label>
                      <MkdInputV2.Field className="!border-[#D1D5DB] !bg-gray-100 !text-gray-600" />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <InteractiveButton
                  type="submit"
                  loading={saving}
                  disabled={saving}
                  className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                >
                  Save Changes
                </InteractiveButton>
              </div>
            </form>
          </div>

          {/* Security Settings Section */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Security Settings
            </h2>

            {/* Password Section */}
            <div className="border-b border-gray-200 pb-6 mb-6">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Password
                  </h3>
                  <p className="text-sm text-gray-500">
                    Last changed: January 10, 2025
                  </p>
                </div>
                <InteractiveButton
                  type="button"
                  onClick={() => setShowPasswordForm(!showPasswordForm)}
                  className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50"
                >
                  {showPasswordForm ? "Cancel" : "Change Password"}
                </InteractiveButton>
              </div>

              {/* Password Change Form */}
              {showPasswordForm && (
                <form
                  onSubmit={handlePasswordSubmit(onPasswordSubmit)}
                  className="space-y-4"
                >
                  <div>
                    <MkdInputV2
                      {...registerPassword("current_password")}
                      errors={passwordErrors.current_password?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>Current Password*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          type="password"
                          placeholder="Enter your current password"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPassword("new_password")}
                      errors={passwordErrors.new_password?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>New Password*</MkdInputV2.Label>
                        <MkdInputV2.Field
                          type="password"
                          placeholder="Enter your new password"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div>
                    <MkdInputV2
                      {...registerPassword("confirm_password")}
                      errors={passwordErrors.confirm_password?.message}
                    >
                      <MkdInputV2.Container>
                        <MkdInputV2.Label>
                          Confirm New Password*
                        </MkdInputV2.Label>
                        <MkdInputV2.Field
                          type="password"
                          placeholder="Confirm your new password"
                          className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                        />
                        <MkdInputV2.Error />
                      </MkdInputV2.Container>
                    </MkdInputV2>
                  </div>

                  <div className="flex justify-end gap-3">
                    <InteractiveButton
                      type="button"
                      onClick={() => {
                        setShowPasswordForm(false);
                        resetPasswordForm();
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md text-sm"
                    >
                      Cancel
                    </InteractiveButton>
                    <InteractiveButton
                      type="submit"
                      loading={saving}
                      disabled={saving}
                      className="px-4 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white rounded-md text-sm"
                    >
                      Update Password
                    </InteractiveButton>
                  </div>
                </form>
              )}
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Seller Verification
            </h3>

            <div className="space-y-4">
              {/* Government ID */}
              <div className="border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-800">Government ID</h4>
                  <div className="flex items-center mt-1">
                    {verificationStatus?.government_id_status ? (
                      <>
                        <span
                          className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                            verificationStatus.government_id_status ===
                            "approved"
                              ? "bg-green-100 text-green-700"
                              : verificationStatus.government_id_status ===
                                  "rejected"
                                ? "bg-red-100 text-red-700"
                                : "bg-yellow-100 text-yellow-700"
                          }`}
                        >
                          {verificationStatus.government_id_status ===
                          "approved"
                            ? "Verified"
                            : verificationStatus.government_id_status ===
                                "rejected"
                              ? "Rejected"
                              : "Pending"}
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          {verificationStatus.government_id_status ===
                            "approved" && "ID verified successfully"}
                          {verificationStatus.government_id_status ===
                            "rejected" &&
                            (verificationStatus.government_id_message ||
                              "Please re-upload your ID")}
                          {verificationStatus.government_id_status ===
                            "pending" && "Under review"}
                        </p>
                      </>
                    ) : (
                      <>
                        <span className="text-xs font-medium bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">
                          Not Uploaded
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          Please upload your government ID
                        </p>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  {verificationStatus?.government_id_status === "approved" ? (
                    <CircleCheckMarkIcon className="w-6 h-6 text-green-500" />
                  ) : (
                    <label className="cursor-pointer">
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleDocumentUpload(file, "government_id");
                          }
                        }}
                        disabled={uploadingDocument.government_id}
                      />
                      <InteractiveButton
                        type="button"
                        loading={uploadingDocument.government_id}
                        disabled={uploadingDocument.government_id}
                        className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 flex items-center"
                      >
                        <CloudUploadIcon className="w-4 h-4 mr-2" />
                        {verificationStatus?.government_id_status
                          ? "Re-upload ID"
                          : "Upload ID"}
                      </InteractiveButton>
                    </label>
                  )}
                </div>
              </div>

              {/* Selfie Verification */}
              <div className="border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-800">
                    Selfie Verification
                  </h4>
                  <div className="flex items-center mt-1">
                    {verificationStatus?.selfie_status ? (
                      <>
                        <span
                          className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                            verificationStatus.selfie_status === "approved"
                              ? "bg-green-100 text-green-700"
                              : verificationStatus.selfie_status === "rejected"
                                ? "bg-red-100 text-red-700"
                                : "bg-yellow-100 text-yellow-700"
                          }`}
                        >
                          {verificationStatus.selfie_status === "approved"
                            ? "Verified"
                            : verificationStatus.selfie_status === "rejected"
                              ? "Rejected"
                              : "Pending"}
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          {verificationStatus.selfie_status === "approved" &&
                            "Selfie verified successfully"}
                          {verificationStatus.selfie_status === "rejected" &&
                            (verificationStatus.selfie_message ||
                              "Please retake your selfie")}
                          {verificationStatus.selfie_status === "pending" &&
                            "Under review"}
                        </p>
                      </>
                    ) : (
                      <>
                        <span className="text-xs font-medium bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">
                          Not Uploaded
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          Please upload your selfie
                        </p>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  {verificationStatus?.selfie_status === "approved" ? (
                    <CircleCheckMarkIcon className="w-6 h-6 text-green-500" />
                  ) : (
                    <label className="cursor-pointer">
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleDocumentUpload(file, "selfie_verification");
                          }
                        }}
                        disabled={uploadingDocument.selfie_verification}
                      />
                      <InteractiveButton
                        type="button"
                        loading={uploadingDocument.selfie_verification}
                        disabled={uploadingDocument.selfie_verification}
                        className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 flex items-center"
                      >
                        <CloudUploadIcon className="w-4 h-4 mr-2" />
                        {verificationStatus?.selfie_status
                          ? "Retake Selfie"
                          : "Upload Selfie"}
                      </InteractiveButton>
                    </label>
                  )}
                </div>
              </div>

              {/* Voice Verification */}
              <div className="border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-gray-800">
                    Voice Verification
                  </h4>
                  <div className="flex items-center mt-1">
                    {verificationStatus?.voice_status ? (
                      <>
                        <span
                          className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                            verificationStatus.voice_status === "approved"
                              ? "bg-green-100 text-green-700"
                              : verificationStatus.voice_status === "rejected"
                                ? "bg-red-100 text-red-700"
                                : "bg-yellow-100 text-yellow-700"
                          }`}
                        >
                          {verificationStatus.voice_status === "approved"
                            ? "Verified"
                            : verificationStatus.voice_status === "rejected"
                              ? "Rejected"
                              : "Pending"}
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          {verificationStatus.voice_status === "approved" &&
                            "Voice verified successfully"}
                          {verificationStatus.voice_status === "rejected" &&
                            (verificationStatus.voice_message ||
                              "Please re-record your voice")}
                          {verificationStatus.voice_status === "pending" &&
                            "Under review"}
                        </p>
                      </>
                    ) : (
                      <>
                        <span className="text-xs font-medium bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">
                          Not Recorded
                        </span>
                        <p className="text-sm text-gray-600 ml-2">
                          Please record your voice verification
                        </p>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {verificationStatus?.voice_status === "approved" ? (
                    <CircleCheckMarkIcon className="w-6 h-6 text-green-500" />
                  ) : (
                    <>
                      <InteractiveButton
                        type="button"
                        onClick={handleVoiceRecording}
                        className={`px-4 py-2 rounded-md text-sm font-medium flex items-center ${
                          isRecording
                            ? "bg-red-500 text-white hover:bg-red-600"
                            : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        <MicrophoneIcon className="w-4 h-4 mr-2" />
                        {isRecording ? "Stop Recording" : "Start Recording"}
                      </InteractiveButton>
                      <label className="cursor-pointer">
                        <input
                          type="file"
                          accept="audio/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleDocumentUpload(file, "voice_verification");
                            }
                          }}
                          disabled={uploadingDocument.voice_verification}
                        />
                        <InteractiveButton
                          type="button"
                          loading={uploadingDocument.voice_verification}
                          disabled={uploadingDocument.voice_verification}
                          className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 flex items-center"
                        >
                          <CloudUploadIcon className="w-4 h-4 mr-2" />
                          Upload Audio
                        </InteractiveButton>
                      </label>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Submit for Review Button */}
            <div className="mt-8 flex flex-col items-center">
              <InteractiveButton
                type="button"
                onClick={handleSubmitForReview}
                loading={saving}
                disabled={saving}
                className="bg-[#1C3D6D] hover:bg-[#1C3D6D]/90 text-white px-8 py-3 rounded-md flex items-center text-base font-semibold"
              >
                <PaperPlaneIcon className="w-5 h-5 mr-2" />
                Submit for Review
              </InteractiveButton>
              <p className="text-center text-xs text-gray-500 mt-3">
                Please ensure all documents are clear and well-lit before
                submission
              </p>
            </div>
          </div>

          {/* Become an eBa Delivery Agent Section */}
          <div className="bg-white rounded-lg p-6">
            {deliveryAgentStatus === "not_registered" ? (
              <>
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">🚚</span>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Become an eBa Delivery Agent
                  </h2>
                </div>

                <p className="text-gray-600 mb-4">
                  Join eBa as a verified delivery partner and earn money helping
                  users receive their purchases safely.
                </p>

                <div className="flex justify-between items-center mb-4">
                  <div>
                    <span className="text-sm font-medium text-gray-700">
                      Status:{" "}
                    </span>
                    <span className="text-sm text-gray-600">
                      Not Registered
                    </span>
                  </div>
                  <InteractiveButton
                    type="button"
                    onClick={() => setShowDeliveryModal(true)}
                    className="bg-[#E63946] hover:bg-[#E63946]/90 text-white px-6 py-2 rounded-md"
                  >
                    Register Now
                  </InteractiveButton>
                </div>

                <p className="text-xs text-gray-500">
                  Complete registration to become a delivery agent
                </p>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      By registering as a delivery agent, you agree to our{" "}
                    </span>
                    <div className="flex gap-2">
                      <button className="text-[#E63946] hover:underline text-sm">
                        Delivery Terms and Conditions
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="flex gap-4">
                      <button
                        onClick={() => setShowDeleteConfirm(true)}
                        className="text-[#E63946] hover:underline text-sm"
                      >
                        Delete Account
                      </button>
                      <button
                        onClick={handleExportData}
                        disabled={saving}
                        className="text-[#0F2C59] hover:underline text-sm disabled:opacity-50"
                      >
                        {saving ? "Exporting..." : "Export My Data"}
                      </button>
                    </div>
                    <InteractiveButton
                      type="button"
                      className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                    >
                      Save All Changes
                    </InteractiveButton>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Pending Application Status */}
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">🚚</span>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Become an eBa Delivery Agent
                  </h2>
                </div>

                <p className="text-gray-600 mb-6">
                  Join eBa as a verified delivery partner and earn while helping
                  users receive their purchases safely.
                </p>

                {/* Status Section */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Status:
                    </span>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-600 mr-2">
                        Pending Approval
                      </span>
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">ℹ</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    Your request is pending admin approval. After verification
                    of your documents, you will see an updated status here.
                  </p>
                </div>

                {/* Verification Items */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center">
                    <span className="text-lg mr-3">🆔</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        ID Verification:{" "}
                      </span>
                      <span className="text-sm text-[#0F2C59]">
                        Under Review
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="text-lg mr-3">🚗</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        Vehicle Documentation:{" "}
                      </span>
                      <span className="text-sm text-[#0F2C59]">
                        Under Review
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="text-lg mr-3">👤</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        Background Check:{" "}
                      </span>
                      <span className="text-sm text-orange-600">Pending</span>
                    </div>
                  </div>
                </div>

                {/* What happens next */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#0F2C59] rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">ℹ</span>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        What happens next?
                      </h4>
                      <p className="text-sm text-gray-700">
                        Our team is reviewing your application. This process
                        typically takes 2-3 business days. You'll receive an
                        email notification once a decision has been made.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Submission Details */}
                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <span>Submitted on: April 18, 2025</span>
                  <button className="text-[#0F2C59] hover:underline">
                    View Submission
                  </button>
                </div>

                {/* Terms and Delete Account */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      By registering as a delivery agent, you agree to our{" "}
                      <button className="text-[#E63946] hover:underline">
                        Delivery Terms and Conditions
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="flex gap-4">
                      <button
                        onClick={() => setShowDeleteConfirm(true)}
                        className="text-[#E63946] hover:underline text-sm"
                      >
                        Delete Account
                      </button>
                      <button
                        onClick={handleExportData}
                        disabled={saving}
                        className="text-[#0F2C59] hover:underline text-sm disabled:opacity-50"
                      >
                        {saving ? "Exporting..." : "Export My Data"}
                      </button>
                    </div>
                    <InteractiveButton
                      type="button"
                      className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                    >
                      Save All Changes
                    </InteractiveButton>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Delivery Partner Modal */}
      <DeliveryPartnerModal
        isOpen={showDeliveryModal}
        onClose={() => setShowDeliveryModal(false)}
        onSubmit={handleDeliveryApplicationSubmit}
      />

      {/* Delete Account Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Delete Account
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete your account? This action cannot
              be undone. All your data, including profile information,
              transactions, and delivery history will be permanently removed.
            </p>
            <div className="flex justify-end gap-3">
              <InteractiveButton
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md"
              >
                Cancel
              </InteractiveButton>
              <InteractiveButton
                type="button"
                onClick={handleDeleteAccount}
                loading={saving}
                disabled={saving}
                className="px-4 py-2 bg-[#E63946] hover:bg-[#E63946]/90 text-white rounded-md"
              >
                Delete Account
              </InteractiveButton>
            </div>
          </div>
        </div>
      )}
    </MemberWrapper>
  );
};

export default MemberProfilePage;
